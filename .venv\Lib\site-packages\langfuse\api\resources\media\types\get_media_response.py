# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import deep_union_pydantic_dicts, pydantic_v1


class GetMediaResponse(pydantic_v1.BaseModel):
    media_id: str = pydantic_v1.Field(alias="mediaId")
    """
    The unique langfuse identifier of a media record
    """

    content_type: str = pydantic_v1.Field(alias="contentType")
    """
    The MIME type of the media record
    """

    content_length: int = pydantic_v1.Field(alias="contentLength")
    """
    The size of the media record in bytes
    """

    uploaded_at: dt.datetime = pydantic_v1.Field(alias="uploadedAt")
    """
    The date and time when the media record was uploaded
    """

    url: str = pydantic_v1.Field()
    """
    The download URL of the media record
    """

    url_expiry: str = pydantic_v1.Field(alias="urlExpiry")
    """
    The expiry date and time of the media record download URL
    """

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        kwargs_with_defaults_exclude_none: typing.Any = {
            "by_alias": True,
            "exclude_none": True,
            **kwargs,
        }

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset),
            super().dict(**kwargs_with_defaults_exclude_none),
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
