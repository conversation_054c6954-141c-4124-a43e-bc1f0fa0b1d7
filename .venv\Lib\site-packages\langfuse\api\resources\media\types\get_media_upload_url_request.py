# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import deep_union_pydantic_dicts, pydantic_v1
from .media_content_type import MediaContentType


class GetMediaUploadUrlRequest(pydantic_v1.BaseModel):
    trace_id: str = pydantic_v1.Field(alias="traceId")
    """
    The trace ID associated with the media record
    """

    observation_id: typing.Optional[str] = pydantic_v1.Field(
        alias="observationId", default=None
    )
    """
    The observation ID associated with the media record. If the media record is associated directly with a trace, this will be null.
    """

    content_type: MediaContentType = pydantic_v1.Field(alias="contentType")
    content_length: int = pydantic_v1.Field(alias="contentLength")
    """
    The size of the media record in bytes
    """

    sha_256_hash: str = pydantic_v1.Field(alias="sha256Hash")
    """
    The SHA-256 hash of the media record
    """

    field: str = pydantic_v1.Field()
    """
    The trace / observation field the media record is associated with. This can be one of `input`, `output`, `metadata`
    """

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        kwargs_with_defaults_exclude_none: typing.Any = {
            "by_alias": True,
            "exclude_none": True,
            **kwargs,
        }

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset),
            super().dict(**kwargs_with_defaults_exclude_none),
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
