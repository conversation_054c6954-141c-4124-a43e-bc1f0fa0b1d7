# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ScoreDataType(str, enum.Enum):
    NUMERIC = "NUMERIC"
    BOOLEAN = "BOOLEAN"
    CATEGORICAL = "CATEGORIC<PERSON>"

    def visit(
        self,
        numeric: typing.Callable[[], T_Result],
        boolean: typing.Callable[[], T_Result],
        categorical: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ScoreDataType.NUMERIC:
            return numeric()
        if self is ScoreDataType.BOOLEAN:
            return boolean()
        if self is ScoreDataType.CATEGORICAL:
            return categorical()
