# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import AsyncClientWrapper, SyncClientWrapper
from ...core.pydantic_utilities import pydantic_v1
from ...core.request_options import RequestOptions
from ..commons.errors.access_denied_error import AccessDeniedError
from ..commons.errors.error import Error
from ..commons.errors.method_not_allowed_error import MethodNotAllowedError
from ..commons.errors.not_found_error import NotFoundError
from ..commons.errors.unauthorized_error import UnauthorizedError
from .types.metrics_response import MetricsResponse


class MetricsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def metrics(
        self, *, query: str, request_options: typing.Optional[RequestOptions] = None
    ) -> MetricsResponse:
        """
        Get metrics from the Langfuse project using a query object

        Parameters
        ----------
        query : str
            JSON string containing the query parameters with the following structure:
            ```json
            {
              "view": string,           // Required. One of "traces", "observations", "scores-numeric", "scores-categorical"
              "dimensions": [           // Optional. Default: []
                {
                  "field": string       // Field to group by, e.g. "name", "userId", "sessionId"
                }
              ],
              "metrics": [              // Required. At least one metric must be provided
                {
                  "measure": string,    // What to measure, e.g. "count", "latency", "value"
                  "aggregation": string // How to aggregate, e.g. "count", "sum", "avg", "p95", "histogram"
                }
              ],
              "filters": [              // Optional. Default: []
                {
                  "column": string,     // Column to filter on
                  "operator": string,   // Operator, e.g. "=", ">", "<", "contains"
                  "value": any,         // Value to compare against
                  "type": string,       // Data type, e.g. "string", "number", "stringObject"
                  "key": string         // Required only when filtering on metadata
                }
              ],
              "timeDimension": {        // Optional. Default: null. If provided, results will be grouped by time
                "granularity": string   // One of "minute", "hour", "day", "week", "month", "auto"
              },
              "fromTimestamp": string,  // Required. ISO datetime string for start of time range
              "toTimestamp": string,    // Required. ISO datetime string for end of time range
              "orderBy": [              // Optional. Default: null
                {
                  "field": string,      // Field to order by
                  "direction": string   // "asc" or "desc"
                }
              ],
              "config": {               // Optional. Query-specific configuration
                "bins": number,         // Optional. Number of bins for histogram (1-100), default: 10
                "row_limit": number     // Optional. Row limit for results (1-1000)
              }
            }
            ```

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        MetricsResponse

        Examples
        --------
        from langfuse.client import FernLangfuse

        client = FernLangfuse(
            x_langfuse_sdk_name="YOUR_X_LANGFUSE_SDK_NAME",
            x_langfuse_sdk_version="YOUR_X_LANGFUSE_SDK_VERSION",
            x_langfuse_public_key="YOUR_X_LANGFUSE_PUBLIC_KEY",
            username="YOUR_USERNAME",
            password="YOUR_PASSWORD",
            base_url="https://yourhost.com/path/to/api",
        )
        client.metrics.metrics(
            query="query",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "api/public/metrics",
            method="GET",
            params={"query": query},
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return pydantic_v1.parse_obj_as(MetricsResponse, _response.json())  # type: ignore
            if _response.status_code == 400:
                raise Error(pydantic_v1.parse_obj_as(typing.Any, _response.json()))  # type: ignore
            if _response.status_code == 401:
                raise UnauthorizedError(
                    pydantic_v1.parse_obj_as(typing.Any, _response.json())
                )  # type: ignore
            if _response.status_code == 403:
                raise AccessDeniedError(
                    pydantic_v1.parse_obj_as(typing.Any, _response.json())
                )  # type: ignore
            if _response.status_code == 405:
                raise MethodNotAllowedError(
                    pydantic_v1.parse_obj_as(typing.Any, _response.json())
                )  # type: ignore
            if _response.status_code == 404:
                raise NotFoundError(
                    pydantic_v1.parse_obj_as(typing.Any, _response.json())
                )  # type: ignore
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncMetricsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def metrics(
        self, *, query: str, request_options: typing.Optional[RequestOptions] = None
    ) -> MetricsResponse:
        """
        Get metrics from the Langfuse project using a query object

        Parameters
        ----------
        query : str
            JSON string containing the query parameters with the following structure:
            ```json
            {
              "view": string,           // Required. One of "traces", "observations", "scores-numeric", "scores-categorical"
              "dimensions": [           // Optional. Default: []
                {
                  "field": string       // Field to group by, e.g. "name", "userId", "sessionId"
                }
              ],
              "metrics": [              // Required. At least one metric must be provided
                {
                  "measure": string,    // What to measure, e.g. "count", "latency", "value"
                  "aggregation": string // How to aggregate, e.g. "count", "sum", "avg", "p95", "histogram"
                }
              ],
              "filters": [              // Optional. Default: []
                {
                  "column": string,     // Column to filter on
                  "operator": string,   // Operator, e.g. "=", ">", "<", "contains"
                  "value": any,         // Value to compare against
                  "type": string,       // Data type, e.g. "string", "number", "stringObject"
                  "key": string         // Required only when filtering on metadata
                }
              ],
              "timeDimension": {        // Optional. Default: null. If provided, results will be grouped by time
                "granularity": string   // One of "minute", "hour", "day", "week", "month", "auto"
              },
              "fromTimestamp": string,  // Required. ISO datetime string for start of time range
              "toTimestamp": string,    // Required. ISO datetime string for end of time range
              "orderBy": [              // Optional. Default: null
                {
                  "field": string,      // Field to order by
                  "direction": string   // "asc" or "desc"
                }
              ],
              "config": {               // Optional. Query-specific configuration
                "bins": number,         // Optional. Number of bins for histogram (1-100), default: 10
                "row_limit": number     // Optional. Row limit for results (1-1000)
              }
            }
            ```

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        MetricsResponse

        Examples
        --------
        import asyncio

        from langfuse.client import AsyncFernLangfuse

        client = AsyncFernLangfuse(
            x_langfuse_sdk_name="YOUR_X_LANGFUSE_SDK_NAME",
            x_langfuse_sdk_version="YOUR_X_LANGFUSE_SDK_VERSION",
            x_langfuse_public_key="YOUR_X_LANGFUSE_PUBLIC_KEY",
            username="YOUR_USERNAME",
            password="YOUR_PASSWORD",
            base_url="https://yourhost.com/path/to/api",
        )


        async def main() -> None:
            await client.metrics.metrics(
                query="query",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "api/public/metrics",
            method="GET",
            params={"query": query},
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return pydantic_v1.parse_obj_as(MetricsResponse, _response.json())  # type: ignore
            if _response.status_code == 400:
                raise Error(pydantic_v1.parse_obj_as(typing.Any, _response.json()))  # type: ignore
            if _response.status_code == 401:
                raise UnauthorizedError(
                    pydantic_v1.parse_obj_as(typing.Any, _response.json())
                )  # type: ignore
            if _response.status_code == 403:
                raise AccessDeniedError(
                    pydantic_v1.parse_obj_as(typing.Any, _response.json())
                )  # type: ignore
            if _response.status_code == 405:
                raise MethodNotAllowedError(
                    pydantic_v1.parse_obj_as(typing.Any, _response.json())
                )  # type: ignore
            if _response.status_code == 404:
                raise NotFoundError(
                    pydantic_v1.parse_obj_as(typing.Any, _response.json())
                )  # type: ignore
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
