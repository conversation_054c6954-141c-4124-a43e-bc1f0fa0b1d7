"""
Pydantic models for loan data structures.
"""

from typing import Optional, List, Dict, Any, Literal
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum


class LoanGrade(str, Enum):
    """Loan grade enumeration."""
    A = "A"
    B = "B"
    C = "C"
    D = "D"
    E = "E"
    F = "F"
    G = "G"


class HomeOwnership(str, Enum):
    """Home ownership status enumeration."""
    RENT = "RENT"
    OWN = "OWN"
    MORTGAGE = "MORTGAGE"
    OTHER = "OTHER"


class VerificationStatus(str, Enum):
    """Income verification status enumeration."""
    VERIFIED = "Verified"
    SOURCE_VERIFIED = "Source Verified"
    NOT_VERIFIED = "Not Verified"


class LoanPurpose(str, Enum):
    """Loan purpose enumeration."""
    DEBT_CONSOLIDATION = "debt_consolidation"
    CREDIT_CARD = "credit_card"
    HOME_IMPROVEMENT = "home_improvement"
    MAJOR_PURCHASE = "major_purchase"
    SMALL_BUSINESS = "small_business"
    CAR = "car"
    WEDDING = "wedding"
    VACATION = "vacation"
    MOVING = "moving"
    HOUSE = "house"
    MEDICAL = "medical"
    EDUCATIONAL = "educational"
    RENEWABLE_ENERGY = "renewable_energy"
    OTHER = "other"


class LoanStatus(str, Enum):
    """Loan status enumeration."""
    FULLY_PAID = "Fully Paid"
    CURRENT = "Current"
    CHARGED_OFF = "Charged Off"
    LATE_31_120 = "Late (31-120 days)"
    IN_GRACE_PERIOD = "In Grace Period"
    LATE_16_30 = "Late (16-30 days)"
    DEFAULT = "Default"


class LoanData(BaseModel):
    """
    Pydantic model for loan application data.
    Based on Lending Club dataset structure.
    """
    
    # Core loan information
    loan_amnt: float = Field(..., description="The listed amount of the loan applied for by the borrower")
    int_rate: float = Field(..., description="Interest Rate on the loan")
    installment: Optional[float] = Field(None, description="The monthly payment owed by the borrower")
    grade: LoanGrade = Field(..., description="LC assigned loan grade")
    sub_grade: Optional[str] = Field(None, description="LC assigned loan subgrade")
    
    # Borrower information
    annual_inc: float = Field(..., description="The self-reported annual income provided by the borrower")
    dti: float = Field(..., description="Debt-to-income ratio")
    emp_length: Optional[str] = Field(None, description="Employment length in years")
    emp_title: Optional[str] = Field(None, description="The job title supplied by the Borrower")
    home_ownership: HomeOwnership = Field(..., description="The home ownership status")
    verification_status: VerificationStatus = Field(..., description="Income verification status")
    
    # Credit information
    fico_range_low: int = Field(..., description="The lower boundary range the borrower's FICO belongs to")
    fico_range_high: int = Field(..., description="The upper boundary range the borrower's FICO belongs to")
    delinq_2yrs: Optional[int] = Field(0, description="Number of 30+ days past-due incidences in the past 2 years")
    inq_last_6mths: Optional[int] = Field(0, description="Number of inquiries in past 6 months")
    pub_rec: Optional[int] = Field(0, description="Number of derogatory public records")
    revol_bal: Optional[float] = Field(None, description="Total credit revolving balance")
    revol_util: Optional[float] = Field(None, description="Revolving line utilization rate")
    
    # Loan details
    purpose: LoanPurpose = Field(..., description="A category provided by the borrower for the loan request")
    title: Optional[str] = Field(None, description="The loan title provided by the borrower")
    addr_state: Optional[str] = Field(None, description="The state provided by the borrower")
    
    # Target variable (for training data)
    loan_status: Optional[LoanStatus] = Field(None, description="Current status of the loan")
    
    # Derived features
    fico_avg: Optional[float] = Field(None, description="Average FICO score")
    loan_to_income_ratio: Optional[float] = Field(None, description="Loan amount to annual income ratio")
    emp_length_numeric: Optional[float] = Field(None, description="Employment length in numeric years")
    
    @validator('fico_avg', always=True)
    def calculate_fico_avg(cls, v, values):
        """Calculate average FICO score."""
        if v is None and 'fico_range_low' in values and 'fico_range_high' in values:
            return (values['fico_range_low'] + values['fico_range_high']) / 2
        return v
    
    @validator('loan_to_income_ratio', always=True)
    def calculate_loan_to_income(cls, v, values):
        """Calculate loan to income ratio."""
        if v is None and 'loan_amnt' in values and 'annual_inc' in values and values['annual_inc'] > 0:
            return values['loan_amnt'] / values['annual_inc']
        # Avoid division by zero, return a large number or None
        elif v is None:
            return None
        return v
    
    @validator('emp_length_numeric', always=True)
    def parse_employment_length(cls, v, values):
        """Parse employment length to numeric value."""
        if v is None and 'emp_length' in values:
            emp_length = values['emp_length']
            if not emp_length or emp_length == 'n/a':
                return 0.0
            elif '< 1 year' in str(emp_length):
                return 0.5
            elif '10+ years' in str(emp_length):
                return 10.0
            else:
                try:
                    # Extracts the number from strings like "3 years"
                    return float(str(emp_length).split()[0])
                except (ValueError, IndexError):
                    return 0.0
        return v
    
    def is_good_loan(self) -> Optional[bool]:
        """
        Determine if this is a 'good' loan based on status.
        Returns None if loan_status is not available.
        """
        if self.loan_status is None:
            return None
        
        good_statuses = [LoanStatus.FULLY_PAID, LoanStatus.CURRENT]
        return self.loan_status in good_statuses
    
    def to_prompt_context(self) -> str:
        """Convert loan data to a formatted string for LLM prompts."""
        context = f"""
Loan Application Details:
- Loan Amount: ${self.loan_amnt:,.2f}
- Interest Rate: {self.int_rate:.2f}%
- Monthly Payment: {f'${self.installment:,.2f}' if self.installment is not None else 'N/A'}
- Loan Grade: {self.grade.value}{f' ({self.sub_grade})' if self.sub_grade else ''}
- Purpose: {self.purpose.value.replace('_', ' ').title()}

Borrower Profile:
- Annual Income: ${self.annual_inc:,.2f}
- Debt-to-Income Ratio: {self.dti:.2f}%
- Employment Length: {self.emp_length or 'Not specified'}
- Home Ownership: {self.home_ownership.value}
- Income Verification: {self.verification_status.value}

Credit Profile:
- FICO Score Range: {self.fico_range_low}-{self.fico_range_high} (Avg: {f'{self.fico_avg:.0f}' if self.fico_avg is not None else 'N/A'})
- Delinquencies (2 years): {self.delinq_2yrs or 0}
- Credit Inquiries (6 months): {self.inq_last_6mths or 0}
- Public Records: {self.pub_rec or 0}
- Revolving Balance: {f'${self.revol_bal:,.2f}' if self.revol_bal is not None else 'N/A'}
- Revolving Utilization: {f'{self.revol_util:.1f}%' if self.revol_util is not None else 'N/A'}

Key Ratios:
- Loan-to-Income Ratio: {f'{self.loan_to_income_ratio:.3f}' if self.loan_to_income_ratio is not None else 'N/A'}
- Employment Stability: {(self.emp_length_numeric or 0.0):.1f} years
"""
        return context.strip()


class LoanBatch(BaseModel):
    """Model for processing multiple loans."""
    loans: List[LoanData] = Field(..., description="List of loan applications")
    batch_id: Optional[str] = Field(None, description="Unique identifier for this batch")
    created_at: datetime = Field(default_factory=datetime.now, description="Batch creation timestamp")
    
    def __len__(self) -> int:
        return len(self.loans)
    
    def get_good_loans_ratio(self) -> Optional[float]:
        """Calculate the ratio of good loans in this batch."""
        loans_with_status = [loan for loan in self.loans if loan.loan_status is not None]
        if not loans_with_status:
            return None
        
        good_loans = sum(1 for loan in loans_with_status if loan.is_good_loan())
        return good_loans / len(loans_with_status)