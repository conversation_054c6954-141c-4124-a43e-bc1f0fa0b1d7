# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import deep_union_pydantic_dicts, pydantic_v1
from .config_category import ConfigCategory
from .score_data_type import ScoreDataType


class ScoreConfig(pydantic_v1.BaseModel):
    """
    Configuration for a score
    """

    id: str
    name: str
    created_at: dt.datetime = pydantic_v1.Field(alias="createdAt")
    updated_at: dt.datetime = pydantic_v1.Field(alias="updatedAt")
    project_id: str = pydantic_v1.Field(alias="projectId")
    data_type: ScoreDataType = pydantic_v1.Field(alias="dataType")
    is_archived: bool = pydantic_v1.Field(alias="isArchived")
    """
    Whether the score config is archived. Defaults to false
    """

    min_value: typing.Optional[float] = pydantic_v1.Field(
        alias="minValue", default=None
    )
    """
    Sets minimum value for numerical scores. If not set, the minimum value defaults to -∞
    """

    max_value: typing.Optional[float] = pydantic_v1.Field(
        alias="maxValue", default=None
    )
    """
    Sets maximum value for numerical scores. If not set, the maximum value defaults to +∞
    """

    categories: typing.Optional[typing.List[ConfigCategory]] = pydantic_v1.Field(
        default=None
    )
    """
    Configures custom categories for categorical scores
    """

    description: typing.Optional[str] = None

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        kwargs_with_defaults_exclude_none: typing.Any = {
            "by_alias": True,
            "exclude_none": True,
            **kwargs,
        }

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset),
            super().dict(**kwargs_with_defaults_exclude_none),
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
