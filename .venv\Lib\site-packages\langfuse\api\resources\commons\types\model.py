# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ....core.datetime_utils import serialize_datetime
from ....core.pydantic_utilities import deep_union_pydantic_dicts, pydantic_v1
from .model_price import ModelPrice
from .model_usage_unit import ModelUsageUnit


class Model(pydantic_v1.BaseModel):
    """
    Model definition used for transforming usage into USD cost and/or tokenization.
    """

    id: str
    model_name: str = pydantic_v1.Field(alias="modelName")
    """
    Name of the model definition. If multiple with the same name exist, they are applied in the following order: (1) custom over built-in, (2) newest according to startTime where model.startTime<observation.startTime
    """

    match_pattern: str = pydantic_v1.Field(alias="matchPattern")
    """
    Regex pattern which matches this model definition to generation.model. Useful in case of fine-tuned models. If you want to exact match, use `(?i)^modelname$`
    """

    start_date: typing.Optional[dt.datetime] = pydantic_v1.Field(
        alias="startDate", default=None
    )
    """
    Apply only to generations which are newer than this ISO date.
    """

    unit: typing.Optional[ModelUsageUnit] = pydantic_v1.Field(default=None)
    """
    Unit used by this model.
    """

    input_price: typing.Optional[float] = pydantic_v1.Field(
        alias="inputPrice", default=None
    )
    """
    Deprecated. See 'prices' instead. Price (USD) per input unit
    """

    output_price: typing.Optional[float] = pydantic_v1.Field(
        alias="outputPrice", default=None
    )
    """
    Deprecated. See 'prices' instead. Price (USD) per output unit
    """

    total_price: typing.Optional[float] = pydantic_v1.Field(
        alias="totalPrice", default=None
    )
    """
    Deprecated. See 'prices' instead. Price (USD) per total unit. Cannot be set if input or output price is set.
    """

    tokenizer_id: typing.Optional[str] = pydantic_v1.Field(
        alias="tokenizerId", default=None
    )
    """
    Optional. Tokenizer to be applied to observations which match to this model. See docs for more details.
    """

    tokenizer_config: typing.Optional[typing.Any] = pydantic_v1.Field(
        alias="tokenizerConfig", default=None
    )
    """
    Optional. Configuration for the selected tokenizer. Needs to be JSON. See docs for more details.
    """

    is_langfuse_managed: bool = pydantic_v1.Field(alias="isLangfuseManaged")
    prices: typing.Dict[str, ModelPrice] = pydantic_v1.Field()
    """
    Price (USD) by usage type
    """

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults_exclude_unset: typing.Any = {
            "by_alias": True,
            "exclude_unset": True,
            **kwargs,
        }
        kwargs_with_defaults_exclude_none: typing.Any = {
            "by_alias": True,
            "exclude_none": True,
            **kwargs,
        }

        return deep_union_pydantic_dicts(
            super().dict(**kwargs_with_defaults_exclude_unset),
            super().dict(**kwargs_with_defaults_exclude_none),
        )

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        populate_by_name = True
        extra = pydantic_v1.Extra.allow
        json_encoders = {dt.datetime: serialize_datetime}
