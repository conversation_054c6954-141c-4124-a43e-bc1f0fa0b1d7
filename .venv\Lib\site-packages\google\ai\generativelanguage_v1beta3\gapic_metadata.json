{"comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "python", "libraryPackage": "google.ai.generativelanguage_v1beta3", "protoPackage": "google.ai.generativelanguage.v1beta3", "schema": "1.0", "services": {"DiscussService": {"clients": {"grpc": {"libraryClient": "DiscussServiceClient", "rpcs": {"CountMessageTokens": {"methods": ["count_message_tokens"]}, "GenerateMessage": {"methods": ["generate_message"]}}}, "grpc-async": {"libraryClient": "DiscussServiceAsyncClient", "rpcs": {"CountMessageTokens": {"methods": ["count_message_tokens"]}, "GenerateMessage": {"methods": ["generate_message"]}}}, "rest": {"libraryClient": "DiscussServiceClient", "rpcs": {"CountMessageTokens": {"methods": ["count_message_tokens"]}, "GenerateMessage": {"methods": ["generate_message"]}}}}}, "ModelService": {"clients": {"grpc": {"libraryClient": "ModelServiceClient", "rpcs": {"CreateTunedModel": {"methods": ["create_tuned_model"]}, "DeleteTunedModel": {"methods": ["delete_tuned_model"]}, "GetModel": {"methods": ["get_model"]}, "GetTunedModel": {"methods": ["get_tuned_model"]}, "ListModels": {"methods": ["list_models"]}, "ListTunedModels": {"methods": ["list_tuned_models"]}, "UpdateTunedModel": {"methods": ["update_tuned_model"]}}}, "grpc-async": {"libraryClient": "ModelServiceAsyncClient", "rpcs": {"CreateTunedModel": {"methods": ["create_tuned_model"]}, "DeleteTunedModel": {"methods": ["delete_tuned_model"]}, "GetModel": {"methods": ["get_model"]}, "GetTunedModel": {"methods": ["get_tuned_model"]}, "ListModels": {"methods": ["list_models"]}, "ListTunedModels": {"methods": ["list_tuned_models"]}, "UpdateTunedModel": {"methods": ["update_tuned_model"]}}}, "rest": {"libraryClient": "ModelServiceClient", "rpcs": {"CreateTunedModel": {"methods": ["create_tuned_model"]}, "DeleteTunedModel": {"methods": ["delete_tuned_model"]}, "GetModel": {"methods": ["get_model"]}, "GetTunedModel": {"methods": ["get_tuned_model"]}, "ListModels": {"methods": ["list_models"]}, "ListTunedModels": {"methods": ["list_tuned_models"]}, "UpdateTunedModel": {"methods": ["update_tuned_model"]}}}}}, "PermissionService": {"clients": {"grpc": {"libraryClient": "PermissionServiceClient", "rpcs": {"CreatePermission": {"methods": ["create_permission"]}, "DeletePermission": {"methods": ["delete_permission"]}, "GetPermission": {"methods": ["get_permission"]}, "ListPermissions": {"methods": ["list_permissions"]}, "TransferOwnership": {"methods": ["transfer_ownership"]}, "UpdatePermission": {"methods": ["update_permission"]}}}, "grpc-async": {"libraryClient": "PermissionServiceAsyncClient", "rpcs": {"CreatePermission": {"methods": ["create_permission"]}, "DeletePermission": {"methods": ["delete_permission"]}, "GetPermission": {"methods": ["get_permission"]}, "ListPermissions": {"methods": ["list_permissions"]}, "TransferOwnership": {"methods": ["transfer_ownership"]}, "UpdatePermission": {"methods": ["update_permission"]}}}, "rest": {"libraryClient": "PermissionServiceClient", "rpcs": {"CreatePermission": {"methods": ["create_permission"]}, "DeletePermission": {"methods": ["delete_permission"]}, "GetPermission": {"methods": ["get_permission"]}, "ListPermissions": {"methods": ["list_permissions"]}, "TransferOwnership": {"methods": ["transfer_ownership"]}, "UpdatePermission": {"methods": ["update_permission"]}}}}}, "TextService": {"clients": {"grpc": {"libraryClient": "TextServiceClient", "rpcs": {"BatchEmbedText": {"methods": ["batch_embed_text"]}, "CountTextTokens": {"methods": ["count_text_tokens"]}, "EmbedText": {"methods": ["embed_text"]}, "GenerateText": {"methods": ["generate_text"]}}}, "grpc-async": {"libraryClient": "TextServiceAsyncClient", "rpcs": {"BatchEmbedText": {"methods": ["batch_embed_text"]}, "CountTextTokens": {"methods": ["count_text_tokens"]}, "EmbedText": {"methods": ["embed_text"]}, "GenerateText": {"methods": ["generate_text"]}}}, "rest": {"libraryClient": "TextServiceClient", "rpcs": {"BatchEmbedText": {"methods": ["batch_embed_text"]}, "CountTextTokens": {"methods": ["count_text_tokens"]}, "EmbedText": {"methods": ["embed_text"]}, "GenerateText": {"methods": ["generate_text"]}}}}}}}