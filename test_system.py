#!/usr/bin/env python3
"""
Test script for the loan approval prediction system.
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.models.loan_data import LoanData, LoanGrade, HomeOwnership, VerificationStatus, LoanPurpose
from src.providers.factory import LLMProviderFactory, LLMConfig
from src.approaches.single_frontier import SingleFrontierApproach
from src.evaluation.benchmarking import LoanApprovalEvaluator


def create_sample_loan_data():
    """Create sample loan data for testing."""
    sample_loans = [
        # Good loan example
        LoanData(
            loan_amnt=15000.0,
            int_rate=8.5,
            grade=LoanGrade.B,
            sub_grade="B2",
            annual_inc=75000.0,
            dti=15.2,
            emp_length="5 years",
            home_ownership=HomeOwnership.MORTGAGE,
            verification_status=VerificationStatus.VERIFIED,
            fico_range_low=720,
            fico_range_high=724,
            delinq_2yrs=0,
            inq_last_6mths=1,
            pub_rec=0,
            revol_bal=8500.0,
            revol_util=25.3,
            purpose=LoanPurpose.DEBT_CONSOLIDATION,
            addr_state="CA"
        ),
        
        # Risky loan example
        LoanData(
            loan_amnt=25000.0,
            int_rate=18.5,
            grade=LoanGrade.E,
            sub_grade="E3",
            annual_inc=45000.0,
            dti=35.8,
            emp_length="< 1 year",
            home_ownership=HomeOwnership.RENT,
            verification_status=VerificationStatus.NOT_VERIFIED,
            fico_range_low=620,
            fico_range_high=624,
            delinq_2yrs=2,
            inq_last_6mths=5,
            pub_rec=1,
            revol_bal=15000.0,
            revol_util=85.2,
            purpose=LoanPurpose.CREDIT_CARD,
            addr_state="FL"
        ),
        
        # Medium risk loan
        LoanData(
            loan_amnt=20000.0,
            int_rate=12.5,
            grade=LoanGrade.C,
            sub_grade="C4",
            annual_inc=60000.0,
            dti=22.5,
            emp_length="3 years",
            home_ownership=HomeOwnership.OWN,
            verification_status=VerificationStatus.SOURCE_VERIFIED,
            fico_range_low=680,
            fico_range_high=684,
            delinq_2yrs=1,
            inq_last_6mths=2,
            pub_rec=0,
            revol_bal=12000.0,
            revol_util=45.0,
            purpose=LoanPurpose.HOME_IMPROVEMENT,
            addr_state="TX"
        )
    ]
    
    return sample_loans


def test_single_approach():
    """Test the single frontier approach."""
    print("🧪 Testing Single Frontier Approach")
    print("-" * 40)
    
    # Create sample data
    sample_loans = create_sample_loan_data()
    
    # Test with a mock provider (you would use real API keys)
    try:
        # This would normally use your API key from .env
        config = LLMConfig(
            provider="google",
            model_name="gemini-2.0-flash",
            temperature=0.1,
            max_tokens=1000
        )
        
        provider = LLMProviderFactory.create_provider("google", config)
        approach = SingleFrontierApproach(provider)
        
        print(f"✅ Initialized {approach.name}")
        print(f"   Provider: {provider.provider_name}")
        print(f"   Model: {provider.model_name}")
        
        # Test single prediction
        print(f"\n📊 Testing single prediction...")
        loan = sample_loans[0]  # Good loan
        
        print(f"Loan Details:")
        print(f"  Amount: ${loan.loan_amnt:,.2f}")
        print(f"  Grade: {loan.grade.value}")
        print(f"  FICO: {loan.fico_range_low}-{loan.fico_range_high}")
        print(f"  DTI: {loan.dti:.1f}%")
        
        prediction = approach.predict_single(loan)
        print(f"\nPrediction: {prediction.decision.value}")
        print(f"Confidence: {prediction.confidence.value}")
        print(f"Reasoning: {prediction.reasoning}")
        
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Make sure to set up your .env file with API keys")


def test_data_structures():
    """Test the data structures and validation."""
    print("\n🧪 Testing Data Structures")
    print("-" * 40)
    
    # Test LoanData creation and validation
    sample_loans = create_sample_loan_data()
    
    for i, loan in enumerate(sample_loans):
        print(f"\nLoan {i+1}:")
        print(f"  Amount: ${loan.loan_amnt:,.2f}")
        print(f"  Grade: {loan.grade.value}")
        print(f"  FICO Avg: {loan.fico_avg:.0f}")
        print(f"  Loan-to-Income: {loan.loan_to_income_ratio:.3f}")
        print(f"  Employment: {loan.emp_length_numeric:.1f} years")
        
        # Test prompt generation
        prompt_context = loan.to_prompt_context()
        print(f"  Prompt length: {len(prompt_context)} characters")
    
    print("✅ Data structures working correctly")


def test_evaluation_system():
    """Test the evaluation system."""
    print("\n🧪 Testing Evaluation System")
    print("-" * 40)
    
    evaluator = LoanApprovalEvaluator()
    
    # Create mock predictions and outcomes
    from src.models.llm_response import SingleModelResponse, LoanDecision, ConfidenceLevel, RiskLevel
    
    mock_predictions = [
        SingleModelResponse(
            decision=LoanDecision.APPROVE,
            confidence=ConfidenceLevel.HIGH,
            risk_assessment=RiskLevel.LOW,
            reasoning="Strong credit profile with high FICO score and low DTI",
            key_factors=["High FICO", "Low DTI", "Stable employment"],
            positive_factors=["Excellent credit score", "Low debt burden"],
            negative_factors=[],
            model_name="test_model"
        ),
        SingleModelResponse(
            decision=LoanDecision.DENY,
            confidence=ConfidenceLevel.HIGH,
            risk_assessment=RiskLevel.HIGH,
            reasoning="High risk due to poor credit and high DTI",
            key_factors=["Low FICO", "High DTI", "Recent delinquencies"],
            positive_factors=[],
            negative_factors=["Poor credit history", "High debt burden"],
            model_name="test_model"
        )
    ]
    
    # Mock actual outcomes (True = good loan, False = bad loan)
    actual_outcomes = [True, False]  # Both predictions are correct
    
    # Test batch evaluation
    batch_result = evaluator.evaluate_batch_predictions(
        mock_predictions, actual_outcomes, "Test Approach"
    )
    
    print(f"✅ Batch Evaluation Results:")
    print(f"   Accuracy: {batch_result.accuracy:.3f}")
    print(f"   Precision: {batch_result.precision:.3f}")
    print(f"   Recall: {batch_result.recall:.3f}")
    print(f"   F1 Score: {batch_result.f1_score:.3f}")
    
    # Generate report
    report = evaluator.generate_evaluation_report(batch_result)
    print(f"\n📄 Report generated ({len(report)} characters)")


def create_sample_csv():
    """Create a sample CSV file for testing."""
    print("\n📁 Creating sample CSV file...")
    
    # Create sample data
    np.random.seed(42)
    n_samples = 100
    
    data = {
        'loan_amnt': np.random.uniform(5000, 40000, n_samples),
        'int_rate': np.random.uniform(5.0, 25.0, n_samples),
        'grade': np.random.choice(['A', 'B', 'C', 'D', 'E', 'F', 'G'], n_samples),
        'annual_inc': np.random.uniform(30000, 150000, n_samples),
        'dti': np.random.uniform(5.0, 40.0, n_samples),
        'fico_range_low': np.random.randint(600, 800, n_samples),
        'fico_range_high': np.random.randint(604, 804, n_samples),
        'home_ownership': np.random.choice(['RENT', 'OWN', 'MORTGAGE'], n_samples),
        'verification_status': np.random.choice(['Verified', 'Source Verified', 'Not Verified'], n_samples),
        'purpose': np.random.choice(['debt_consolidation', 'credit_card', 'home_improvement'], n_samples),
        'loan_status': np.random.choice(['Fully Paid', 'Charged Off', 'Current'], n_samples, p=[0.6, 0.3, 0.1])
    }
    
    # Ensure fico_range_high > fico_range_low
    data['fico_range_high'] = data['fico_range_low'] + 4
    
    df = pd.DataFrame(data)
    
    # Save to CSV
    csv_file = "sample_loan_data.csv"
    df.to_csv(csv_file, index=False)
    
    print(f"✅ Created {csv_file} with {n_samples} samples")
    print(f"   Good loans: {sum(df['loan_status'].isin(['Fully Paid', 'Current']))}")
    print(f"   Bad loans: {sum(df['loan_status'] == 'Charged Off')}")
    
    return csv_file


def main():
    """Run all tests."""
    print("🏦 Loan Approval Prediction System - Test Suite")
    print("=" * 60)
    
    # Test data structures
    test_data_structures()
    
    # Test evaluation system
    test_evaluation_system()
    
    # Test single approach (without API calls)
    test_single_approach()
    
    # Create sample CSV
    sample_csv = create_sample_csv()
    
    print(f"\n🎯 Next Steps:")
    print(f"1. Set up your .env file with API keys")
    print(f"2. Run the main system:")
    print(f"   python main.py --csv-file {sample_csv} --approach single --max-records 10")
    print(f"3. Check the results/ directory for outputs")
    
    print(f"\n✅ All tests completed!")


if __name__ == "__main__":
    main()
