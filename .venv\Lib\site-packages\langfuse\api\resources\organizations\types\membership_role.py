# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class MembershipRole(str, enum.Enum):
    OWNER = "OWNER"
    ADMIN = "ADMIN"
    MEMBER = "MEMBER"
    VIEWER = "VIEWER"

    def visit(
        self,
        owner: typing.Callable[[], T_Result],
        admin: typing.Callable[[], T_Result],
        member: typing.Callable[[], T_Result],
        viewer: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is MembershipRole.OWNER:
            return owner()
        if self is MembershipRole.ADMIN:
            return admin()
        if self is MembershipRole.MEMBER:
            return member()
        if self is MembershipRole.VIEWER:
            return viewer()
