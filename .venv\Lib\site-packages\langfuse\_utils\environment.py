"""@private"""

import os

common_release_envs = [
    # Render
    "RENDER_GIT_COMMIT",
    # Git<PERSON>ab CI
    "CI_COMMIT_SHA",
    # <PERSON><PERSON>
    "CIRCLE_SHA1",
    # <PERSON><PERSON>
    "SOURCE_VERSION",
    # <PERSON>
    "TRAVIS_COMMIT",
    # <PERSON> (commonly used variable, but can be customized in Jenkins setups)
    "GIT_COMMIT",
    # GitHub Actions
    "GITHUB_SHA",
    # Bitbucket Pipelines
    "BITBUCKET_COMMIT",
    # Azure Pipelines
    "BUILD_SOURCEVERSION",
    # Drone CI
    "DRONE_COMMIT_SHA",
]


def get_common_release_envs():
    for env in common_release_envs:
        if env in os.environ:
            return os.environ[env]
    return None
